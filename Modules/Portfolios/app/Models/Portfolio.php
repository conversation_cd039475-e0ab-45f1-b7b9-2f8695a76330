<?php

declare(strict_types=1);

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Models\Company;
use App\Models\User;
use App\Models\City;
use App\Models\District;
use App\Models\Neighborhoods;

class Portfolio extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'portfolios';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'id';

    /**
     * The "type" of the auto-incrementing ID.
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'company_id',
        'title',
        'slug',
        'description',
        'agent_id',
        'user_id',
        'owner_id',
        'status',
        'type_id',
        'main_category_id',
        'category_id',
        'details',
        'gross_area',
        'net_area',
        'price_per_m2',
        'from_whom',
        'is_loan_eligible',
        'deed_status',
        'swap',
        'pool',
        'city_id',
        'district_id',
        'neighborhood_id',
        'address',
        'latitude',
        'longitude',
        'price',
        'currency',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'details' => 'array',
        'gross_area' => 'decimal:2',
        'net_area' => 'decimal:2',
        'price_per_m2' => 'decimal:2',
        'price' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_loan_eligible' => 'boolean',
        'swap' => 'boolean',
        'pool' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * Get the company that owns the portfolio.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /**
     * Get the agent assigned to the portfolio.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'agent_id', 'id');
    }

    /**
     * Get the user who created the portfolio.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Get the portfolio type.
     */
    public function type(): BelongsTo
    {
        return $this->belongsTo(PortfolioType::class, 'type_id', 'id');
    }

    /**
     * Get the portfolio category.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(PortfolioCategory::class, 'main_category_id', 'id');
    }
    /**
     * Get the portfolio sub category.
     */
    public function subCategory(): BelongsTo
    {
        return $this->belongsTo(PortfolioCategory::class, 'category_id', 'id');
    }

    /**
     * Get the city.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id', 'il_id');
    }

    /**
     * Get the district.
     */
    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_id', 'ilce_id');
    }

    /**
     * Get the neighborhood.
     */
    public function neighborhood(): BelongsTo
    {
        return $this->belongsTo(Neighborhoods::class, 'neighborhood_id', 'mahalle_id');
    }

    /**
     * Get all images for the portfolio.
     */
    public function images(): MorphMany
    {
        return $this->morphMany(PortfolioImage::class, 'imageable');
    }

    /**
     * Get all documents for the portfolio.
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(PortfolioDocument::class, 'documentable');
    }

    /**
     * Get the portfolio features.
     */
    public function portfolioFeatures(): HasMany
    {
        return $this->hasMany(PortfolioFeature::class, 'portfolio_id', 'id');
    }

    /**
     * Get the features through pivot table.
     */
    public function features(): BelongsToMany
    {
        return $this->belongsToMany(
            PortfolioCategoryFeature::class,
            'portfolio_features',
            'portfolio_id',
            'feature_id'
        )->withTimestamps();
    }

    /**
     * Scope a query to only include active portfolios.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by company.
     */
    public function scopeForCompany($query, string $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by price range.
     */
    public function scopePriceBetween($query, float $min, float $max)
    {
        return $query->whereBetween('price', [$min, $max]);
    }

    /**
     * Get the formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        if (!$this->price) {
            return 'Fiyat Belirtilmemiş';
        }

        $formatted = number_format((float) $this->price, 0, ',', '.');

        return match($this->currency) {
            'USD' => '$' . $formatted,
            'EUR' => '€' . $formatted,
            'TRY' => '₺' . $formatted,
            default => $formatted . ' ' . $this->currency,
        };
    }

    /**
     * Get the status label in Turkish.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'Aktif',
            'sold' => 'Satıldı',
            'rented' => 'Kiralandı',
            'archived' => 'Arşivlendi',
            'inactive' => 'Pasif',
            default => 'Bilinmiyor',
        };
    }

    /**
     * Get the from whom label in Turkish.
     */
    public function getFromWhomLabelAttribute(): string
    {
        return match($this->from_whom) {
            'owner' => 'Sahibinden',
            'agent' => 'Emlakçıdan',
            'developer' => 'Yükleniciden',
            'bank' => 'Bankadan',
            default => 'Belirtilmemiş',
        };
    }
}
