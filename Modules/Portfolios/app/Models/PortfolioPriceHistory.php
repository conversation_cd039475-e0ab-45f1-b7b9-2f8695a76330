<?php

namespace Modules\Portfolios\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\Portfolios\Database\Factories\PortfolioPriceHistoryFactory;

class PortfolioPriceHistory extends Model
{
    use HasFactory;

    protected $table = 'portfolio_price_history';

    protected $fillable = [
        'portfolio_id',
        'company_id',
        'old_price',
        'new_price',
        'changed_by',
    ];

    protected $casts = [
        'old_price' => 'decimal:2',
        'new_price' => 'decimal:2',
    ];

    // protected static function newFactory(): PortfolioPriceHistoryFactory
    // {
    //     // return PortfolioPriceHistoryFactory::new();
    // }
}
